from datetime import datetime, time

import pytz
import recurrence
from django.contrib.gis.geos import Point
from django.utils.timezone import now
from rest_framework import serializers

from .models import (
    ContentType,
    UGCCategory,
    UGCOrganiser,
    UGCUserProfile,
    UserGeneratedContent,
)


class UGCUserProfileSerializer(serializers.ModelSerializer):
    piano_user_id = serializers.CharField()
    user_email = serializers.CharField()
    user_name = serializers.CharField()
    avatar = serializers.ImageField(required=False)

    class Meta:
        model = UGCUserProfile
        fields = [
            "piano_user_id",
            "user_email",
            "user_name",
            "avatar",
            "is_active",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        avatar = instance.avatar

        representation["avatar"] = (
            f"longbeach/{avatar.url.rstrip('/').split('/')[-1]}"
            if avatar
            else None
        )

        return representation


class UGCOrganiserUpdateSerializer(serializers.Serializer):
    user_profile = serializers.CharField()
    organiser_name = serializers.CharField()
    organiser_contact_number = serializers.CharField()
    organiser_email = serializers.CharField()
    organiser_website_url = serializers.CharField(default="", required=False)
    organiser_logo = serializers.ImageField(required=False)


class UGCOrganiserSerializer(serializers.ModelSerializer):
    logo = serializers.SerializerMethodField()

    class Meta:
        model = UGCOrganiser
        fields = "__all__"

    def get_logo(self, obj):
        return (
            f"longbeach/{obj.logo.url.rstrip('/').split('/')[-1]}"
            if obj.logo
            else None
        )


class UGCCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = UGCCategory
        fields = ("id", "name")


class UGCImageUrlField(serializers.RelatedField):
    """Return only the unique part of an image url."""

    def to_representation(self, instance):
        """Get the image URL."""
        try:
            return instance.get_uri()
        except ValueError:
            return None


class UGCImageWithIdField(serializers.RelatedField):
    """Return the image url with the image id."""

    def to_representation(self, instance):
        """Get the image URL."""
        try:
            return {
                "id": instance.id,
                "uri": instance.get_uri(),
            }
        except ValueError:
            return None


class UserGeneratedContentSerializer(serializers.ModelSerializer):
    category = serializers.PrimaryKeyRelatedField(
        queryset=UGCCategory.objects.all()
    )
    user_profile = serializers.PrimaryKeyRelatedField(
        queryset=UGCUserProfile.objects.all(), write_only=True
    )
    organiser = serializers.PrimaryKeyRelatedField(
        queryset=UGCOrganiser.objects.all(), write_only=True, required=False
    )
    latitude = serializers.FloatField(write_only=True, required=False)
    longitude = serializers.FloatField(write_only=True, required=False)

    images = UGCImageUrlField(many=True, read_only=True)
    images_with_id = UGCImageWithIdField(
        source="images", many=True, read_only=True
    )

    category_name = serializers.CharField(
        source="category.name", read_only=True
    )
    user_details = UGCUserProfileSerializer(
        source="user_profile", read_only=True
    )
    organiser_details = UGCOrganiserSerializer(
        source="organiser", read_only=True
    )
    lat = serializers.SerializerMethodField()
    lng = serializers.SerializerMethodField()
    deleted_image_ids = serializers.ListField(
        child=serializers.IntegerField(), write_only=True, required=False
    )

    class Meta:
        model = UserGeneratedContent
        fields = (
            "id",
            "masthead",
            "title",
            "description",
            "start_datetime",
            "end_datetime",
            "content_type",
            "created_on",
            "published_on",
            "location",
            "images",
            "images_with_id",  # keeping both for backwards compatibility, consider migrating to images_with_id only
            "category",
            "category_name",
            "user_details",
            "user_profile",
            "organiser",
            "organiser_details",
            "recurrence_text",
            "next_occurrence",
            "price_text",
            "start_time_text",
            "lat",
            "lng",
            "latitude",
            "longitude",
            "status",
            "deleted_image_ids",
            "recurrences",
        )

    def create(self, validated_data):
        lat = validated_data.pop("latitude", None)
        lng = validated_data.pop("longitude", None)

        if lat is not None and lng is not None:
            validated_data["location_point"] = Point(lng, lat, srid=4326)

        recurrence_rule = self.initial_data.get("recurrence_rule")

        instance = UserGeneratedContent(**validated_data)

        if instance.content_type == ContentType.EVENT:
            instance.next_occurrence = instance.start_datetime
            if recurrence_rule:
                instance.recurrences = recurrence.deserialize(recurrence_rule)

        instance.save()

        return instance

    def update(self, instance, validated_data):
        lat = validated_data.pop("latitude", None)
        lng = validated_data.pop("longitude", None)

        if lat is not None and lng is not None:
            validated_data["location_point"] = Point(lng, lat, srid=4326)

        recurrence_rule = self.initial_data.get("recurrence_rule")

        for attr, value in validated_data.items():
            if getattr(instance, attr, None) != value:
                setattr(instance, attr, value)

        if instance.content_type == ContentType.EVENT:
            instance.next_occurrence = instance.start_datetime
            if recurrence_rule:
                instance.recurrences = recurrence.deserialize(recurrence_rule)

        instance.save()
        return instance

    def to_representation(self, instance):
        """
        If the original start_datetime is in the past but end_datetime is still in the future,
        override start_datetime to be midnight today.
        """
        representation = super().to_representation(instance)
        australia_tz = pytz.timezone("Australia/Sydney")

        if (
            instance.start_datetime
            and instance.start_datetime < now()
            and instance.end_datetime > now()
        ):
            australia_today = now().astimezone(australia_tz).date()
            australia_today_midnight = datetime.combine(
                australia_today, time(0)
            )
            representation["start_datetime"] = australia_tz.localize(
                australia_today_midnight
            ).astimezone(pytz.UTC)

        return representation

    def get_lat(self, obj):
        return obj.location_point.y if obj.location_point else None

    def get_lng(self, obj):
        return obj.location_point.x if obj.location_point else None
