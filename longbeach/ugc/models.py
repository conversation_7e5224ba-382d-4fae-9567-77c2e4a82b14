from datetime import <PERSON><PERSON><PERSON>
from inspect import cleandoc
from typing import Optional, cast

import pytz
import reversion
from autoslug import AutoSlugField
from django.conf import settings
from django.contrib.gis.db import models as gis_models
from django.contrib.gis.geos import Point
from django.core.exceptions import ValidationError
from django.db import models
from django.db.models import Q
from django.template.defaultfilters import filesizeformat
from django.utils.text import slugify
from django.utils.timezone import now
from django.utils.translation import gettext_lazy as _
from geocode.backends import get_backend
from recurrence.fields import RecurrenceField
from valencia_storage import ValenciaStorage

from longbeach.business.manage.utils import get_sites
from longbeach.settings.models import Settings
from longbeach.utils.mail import send_html_email

MAX_LENGTH = 100
MAX_IMAGE_SIZE_BYTES = 10 * 1024 * 1024  # 10MB

valencia_fs = ValenciaStorage()


class ContentType(models.TextChoices):
    EVENT = "event", "Event"
    STORY = "story", "Story"
    PHOTOS = "photos", "Photos"


class NotificationType(models.TextChoices):
    UPCOMING_EVENT = "upcoming_event", "Upcoming Event"
    FOLLOW_UP = "follow_up", "Follow up"


class ImageField(models.ImageField):
    """
    An image field with a maximum file size.

    Django already validates that the file is a valid image.
    """

    def __init__(self, *args, **kwargs):
        self.max_size = kwargs.pop("max_size", MAX_IMAGE_SIZE_BYTES)

        super().__init__(*args, **kwargs)

    def clean(self, *args, **kwargs):
        data = super().clean(*args, **kwargs)

        try:
            file = data.file
        except AttributeError:
            # Files already in Valencia can't be opened. We only want to
            # validate new files anyway
            return data

        if file.size > self.max_size:
            raise ValidationError(
                _("This image is too large. The maximum is %s")
                % filesizeformat(self.max_size),
            )

        return data


class UGCMasthead(models.Model):  # type: ignore[django-manager-missing]
    """
    An object for associating a Suzuka masthead with user-generated content.
    """

    site_id = models.IntegerField(unique=True, primary_key=True)
    location_point = gis_models.PointField(
        geography=True, null=True, blank=True
    )

    def __str__(self):
        return self.site_name or ""

    @property
    def site_name(self):
        site = get_sites().get(self.site_id)
        return site["name"] if site else None

    @property
    def domain(self):
        site = get_sites().get(self.site_id)
        return site["domain"] if site else None


class UGCCategory(models.Model):  # type: ignore[django-manager-missing]
    content_type = models.CharField(
        max_length=MAX_LENGTH, choices=ContentType.choices, blank=True
    )
    name = models.CharField(max_length=MAX_LENGTH, db_index=True)
    slug = AutoSlugField(
        max_length=MAX_LENGTH,
        blank=True,
        default="",
        populate_from="name",
    )

    class Meta:
        verbose_name = _("Category")
        verbose_name_plural = _("Categories")
        ordering = ["name"]

    def __str__(self):
        return f"{self.content_type} - {self.name}"


@reversion.register()
class UGCOrganiser(models.Model):  # type: ignore[django-manager-missing]
    """A model representing an organiser for UGC event content type."""

    name = models.CharField(max_length=MAX_LENGTH)
    email = models.EmailField(max_length=MAX_LENGTH, unique=True)
    contact_number = models.CharField(max_length=MAX_LENGTH, blank=True)
    website_url = models.CharField(max_length=MAX_LENGTH, blank=True)
    logo = ImageField(storage=valencia_fs, upload_to="images", blank=True)

    class Meta:
        verbose_name = _("Organiser Profile")

    def __str__(self):
        return self.name


@reversion.register()
class UGCUserProfile(models.Model):  # type: ignore[django-manager-missing]
    """A model representing a user profile."""

    piano_user_id = models.CharField(
        max_length=MAX_LENGTH, unique=True, primary_key=True
    )
    user_name = models.CharField(max_length=MAX_LENGTH, blank=True)
    user_email = models.EmailField(max_length=MAX_LENGTH, unique=True)
    description = models.CharField(max_length=MAX_LENGTH, blank=True)
    avatar = ImageField(
        storage=valencia_fs,
        upload_to="avatars",
        blank=True,
    )
    is_active = models.BooleanField("Active", default=True)
    organiser = models.ForeignKey(
        UGCOrganiser,
        on_delete=models.CASCADE,
        related_name="user_profiles",
        null=True,
        blank=True,
    )

    class Meta:
        ordering = ("user_name",)
        verbose_name = _("User Profile")

    def __str__(self):
        return self.user_name

    def ban(self, save=True):
        """Disable the user account and notify them."""
        self.is_active = False

        if save:
            self.save()

        if not self.user_email:
            return

        send_html_email(
            "ugc/emails/user_banned",
            {
                "noticeboard_email": settings.ACM_NOTICEBOARD_EMAIL,
                "object": self,
            },
            "Account Action: User Ban Due to Spam",
            (self.user_email,),
        )


class UGCManager(models.Manager["UserGeneratedContent"]):
    def upcoming_events(self):
        now_ = now()
        start_datetime_max = now_ + timedelta(days=1)
        start_datetime_min = now_ - timedelta(days=2)

        return (
            self.get_queryset()
            .filter(
                content_type=ContentType.EVENT,
                start_datetime__gte=start_datetime_min,
                start_datetime__lte=start_datetime_max,
                # Exclude events that have already ended
                end_datetime__gte=now_,
                status=UserGeneratedContent.APPROVED,
                user_profile__is_active=True,
                user_profile__user_email__regex=".",
            )
            .exclude(
                notifications__notification_type=NotificationType.UPCOMING_EVENT,
            )
        )

    def follow_ups(self):
        now_ = now()
        published_on_min = now_ - timedelta(days=9)
        published_on_max = now_ - timedelta(days=7)
        return (
            self.get_queryset()
            .filter(
                # Exclude events that have already ended
                # `None` is for non-events
                Q(end_datetime__gte=now_) | Q(end_datetime=None),
                published_on__gte=published_on_min,
                published_on__lte=published_on_max,
                status=UserGeneratedContent.APPROVED,
                user_profile__is_active=True,
                user_profile__user_email__regex=".",
            )
            .exclude(
                notifications__notification_type=NotificationType.FOLLOW_UP,
            )
        )


@reversion.register(follow=["images", "organiser", "user_profile"])
class UserGeneratedContent(models.Model):
    """
    A model representing a user-generated content.
    """

    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"

    STATUS_CHOICES = (
        (PENDING, "Pending"),
        (APPROVED, "Approved"),
        (REJECTED, "Rejected"),
    )

    STATUS_CLASSNAMES = {
        APPROVED: "label-success",
        PENDING: "",
        REJECTED: "label-important",
    }

    STATUS_EMAIL_SUBJECT = {
        APPROVED: "Your Contribution Has Been Approved!",
        PENDING: "Your Contribution is Under Review",
        REJECTED: "Update on Your Recent Contribution",
    }

    FOLLOW_UP_EMAIL_SUBJECT = (
        "Your Contribution Is Live - We'd Love To See More!"
    )

    FOLLOW_UP_EVENT_EMAIL_SUBJECT = "Your Contribution Is Live & Coming Soon"

    UPCOMING_EVENT_EMAIL_SUBJECT = (
        "Less Than A Day Before Your Event Goes Live!"
    )

    UPCOMING_EVENT_EMAIL_TEXT = (
        # Note: 2 strings.
        UPCOMING_EVENT_EMAIL_SUBJECT,
        "Thank you for helping us foster a strong, inclusive, and "
        "informed community. Your input is greatly appreciated, and we "
        "look forward to seeing more of your valuable contributions.",
    )

    FOLLOW_UP_EMAIL_TEXT = (
        # Note: 3 strings.
        FOLLOW_UP_EMAIL_SUBJECT,
        "We truly appreciate your work in helping to build a strong, "
        "inclusive, and informed community.",
        "If you have more great photos or stories to share, we'd love "
        "to feature them! Your voice and perspective make a difference.",
    )

    FOLLOW_UP_EVENT_EMAIL_TEXT = (
        # 2 strings. 2nd rendered paragraph is hardcoded inside template as isn't static.
        FOLLOW_UP_EVENT_EMAIL_SUBJECT,
        "Thank you for helping us foster a strong, inclusive, and "
        "informed community. Your input is greatly appreciated, and we "
        "look forward to seeing more of your valuable contributions.",
    )

    STATUS_EMAIL_TEXT = {
        # dict with 3 strings. Moderator's note goes between the last two
        APPROVED: (
            STATUS_EMAIL_SUBJECT[APPROVED],
            "We’re excited to inform you that your recent contribution has "
            "been reviewed and approved! It is now live on the platform for "
            "the community to see and engage with.",
            "Thank you for helping us foster a strong, inclusive, and "
            "informed community. Your input is greatly appreciated, and we "
            "look forward to seeing more of your valuable contributions.",
        ),
        PENDING: (
            STATUS_EMAIL_SUBJECT[PENDING],
            "Thank you for your recent contribution to our community "
            "platform. We appreciate your participation in helping to build a "
            "strong, inclusive, and informed community.",
            "Your submission is currently under review by our moderation "
            "team. This process ensures that all contributions adhere to our "
            "guidelines and contribute positively to the community. You’ll "
            "receive another notification once the review is complete.\n\n"
            "Thank you for your patience and for being a valued member of our "
            "community.",
        ),
        REJECTED: (
            STATUS_EMAIL_SUBJECT[REJECTED],
            "Thank you for your recent contribution to our platform. After "
            "careful review, we regret to inform you that your submission did "
            "not meet our community guidelines and has not been approved for "
            "posting.",
            "We encourage you to review our guidelines and continue "
            "contributing to the platform. If you believe this was a mistake, "
            "feel free to reach out to our support team for clarification.\n"
            "We appreciate your understanding and hope to see your "
            "contributions in the future.",
        ),
    }

    masthead = models.ForeignKey(
        UGCMasthead, on_delete=models.deletion.CASCADE
    )
    content_type = models.CharField(
        max_length=MAX_LENGTH, choices=ContentType.choices, blank=True
    )
    category = models.ForeignKey(
        UGCCategory,
        related_name="ugc",
        on_delete=models.deletion.CASCADE,
    )
    user_profile = models.ForeignKey(
        UGCUserProfile, null=True, blank=True, on_delete=models.SET_NULL
    )
    organiser = models.ForeignKey(
        UGCOrganiser, null=True, blank=True, on_delete=models.SET_NULL
    )
    title = models.CharField(max_length=MAX_LENGTH)
    location = models.CharField(max_length=MAX_LENGTH, blank=True)
    start_datetime = models.DateTimeField(blank=True, null=True)
    end_datetime = models.DateTimeField(blank=True, null=True)
    description = models.TextField()
    status = models.CharField(
        max_length=20,
        default=PENDING,
        choices=STATUS_CHOICES,
        help_text="Changing the status will notify the user.",
    )
    created_on = models.DateTimeField(auto_now_add=True)
    published_on = models.DateTimeField(null=True, blank=True)
    recurrences = RecurrenceField(blank=True, null=True)
    next_occurrence = models.DateTimeField(
        blank=True, null=True, db_index=True
    )
    price_text = models.CharField(max_length=MAX_LENGTH, blank=True)
    start_time_text = models.CharField(max_length=50, blank=True)
    location_point = gis_models.PointField(
        geography=True, null=True, blank=True
    )

    objects = UGCManager()

    class Meta:
        verbose_name = _("User Generated Content")
        verbose_name_plural = _("User Generated Content")
        ordering = ("-created_on", "id")

    def __str__(self):
        return self.title

    def clean(self):
        if self.content_type == ContentType.EVENT and not self.organiser:
            raise ValidationError(
                {"organiser": "Organiser is required for UGC Event types."}
            )

    # https://docs.djangoproject.com/en/4.1/ref/models/instances/#customizing-model-loading
    @classmethod
    def from_db(cls, db, field_names, values):
        instance = super().from_db(db, field_names, values)

        db_data = {}
        required_fields = ["status", "location"]
        for index, field_name in enumerate(field_names):
            if field_name in required_fields:
                db_data[field_name] = values[index]
        # save original values, when model is loaded from database,
        # in a separate attribute on the model
        instance._loaded_values = db_data

        return instance

    def save(self, *args, **kwargs):
        loaded_values = getattr(
            self, "_loaded_values", {}
        )  # use getattr to avoid AttributeError on tests

        if (
            self.status == UserGeneratedContent.APPROVED
            and not self.published_on
            and (
                self._state.adding
                or loaded_values.get("status") != UserGeneratedContent.APPROVED
            )
        ):
            self.published_on = now()

        location_changed = (
            not self._state.adding
            and loaded_values.get("location") != self.location
        )

        if location_changed:
            if self.location:
                try:
                    geocoder = get_backend()
                    result = geocoder.geocode(self.location)
                except Exception:
                    result = None
                if result:
                    self.location_point = Point(
                        result.longitude, result.latitude, srid=4326
                    )
                else:
                    self.location_point = None
            else:
                self.location_point = None

        if (
            self.recurrences
            and self.recurrences.rdates == []
            and self.recurrences.rrules == []
        ):
            self.recurrences = None

        return super().save(*args, **kwargs)

    def get_status_classnames(self):
        """Get the CSS classes for the status badge."""
        return UserGeneratedContent.STATUS_CLASSNAMES[self.status]

    def set_status(self, status, note=None):
        if status == UserGeneratedContent.APPROVED:
            self.status = UserGeneratedContent.APPROVED
            self.send_status_email(note)
        elif status == UserGeneratedContent.REJECTED:
            self.status = UserGeneratedContent.REJECTED
            self.send_status_email(note)
        else:
            return

        with reversion.create_revision():
            self.save()
            reversion.set_comment(f"Status changed to {status}")

    def set_next_occurrence(self):
        """
        Calculate and set the nearest upcoming event date (For events only)

        For recurring events, this is the next occurrence date based on the recurrence rules.

        For non-recurring events:
            - If the event has not started, set to start_datetime
            - If the event has started but not ended, set to today
            - If the event has ended, set to None
        """
        if self.content_type != ContentType.EVENT or not self.start_datetime:
            return

        australia_tz = pytz.timezone("Australia/Sydney")
        now_utc = now()
        today = now_utc.astimezone(australia_tz).replace(
            hour=0,
            minute=0,
            second=0,
            microsecond=0,
            tzinfo=None,
        )

        if not self.recurrences:
            if self.start_datetime > now_utc:
                self.next_occurrence = self.start_datetime
            elif self.start_datetime < now_utc and self.end_datetime > now_utc:
                self.next_occurrence = today.replace(
                    tzinfo=pytz.UTC,
                )
            elif self.end_datetime < now_utc:
                self.next_occurrence = None
            self.save()
            return self.next_occurrence

        next_occurrence = self.recurrences.after(
            max(
                today - timedelta(days=1),
                self.start_datetime.replace(tzinfo=None) - timedelta(days=1),
            )
        )

        if next_occurrence:
            self.next_occurrence = next_occurrence.replace(
                hour=0,
                minute=0,
                second=0,
                microsecond=0,
                tzinfo=pytz.UTC,
            )
        else:
            self.next_occurrence = None

        self.save()
        return self.next_occurrence

    def _has_valid_user_profile(self) -> bool:
        return bool(
            self.user_profile is not None
            and self.user_profile.user_email
            and self.user_profile.is_active
        )

    def get_lookup_address(self):
        return self.location

    @property
    def recurrence_text(self) -> Optional[str]:
        return (
            self.recurrences.rrules[0].to_text()
            if self.recurrences and self.recurrences.rrules
            else None
        )

    @property
    def noticeboard_contribute_url(self) -> str:
        return f"https://{self.masthead.domain}/notice-board/contribute/"

    @property
    def start_date_formatted(self) -> str:
        australia_tz = pytz.timezone("Australia/Sydney")
        return (
            self.start_datetime.astimezone(australia_tz).strftime("%A %-d %B")
            if self.start_datetime
            else ""
        )

    @property
    def detail_page_url(self) -> str:
        section: str = {
            ContentType.EVENT: "whats-on",
            ContentType.PHOTOS: "photos",
            ContentType.STORY: "contributions",
        }.get(cast(ContentType, self.content_type), "")

        return (
            f"https://{self.masthead.domain}/notice-board/{section}/"
            f"{self.pk}/{slugify(self.title)}/"
        )

    @property
    def edit_url(self) -> str:
        return f"https://{self.masthead.domain}/notice-board/edit/{self.pk}/{slugify(self.title)}/"

    def send_status_email(self, note=None) -> None:
        """Email the user about the updated publication status."""
        user_email = getattr(self.user_profile, "user_email", None)
        if not user_email:
            return None

        subject = UserGeneratedContent.STATUS_EMAIL_SUBJECT[self.status]

        assert self.user_profile is not None

        context = {
            "share_url": self.status == UserGeneratedContent.APPROVED
            and self.detail_page_url,
            "edit_url": self.user_profile.is_active and self.edit_url,
            "noticeboard_email": settings.ACM_NOTICEBOARD_EMAIL,
            "object": self,
            "note": note,
            "site_name": self.masthead.site_name,
            "text": [
                cleandoc(text)
                for text in UserGeneratedContent.STATUS_EMAIL_TEXT[self.status]
            ],
        }

        send_html_email(
            "ugc/emails/ugc_status",
            context,
            subject,
            (self.user_profile.user_email,),
        )

    def send_new_content_email(self) -> None:
        # Note: We don't allow the settings field email override to be used
        # in production.
        to_email = (
            settings_mail_to or settings.ACM_NOTICEBOARD_EMAIL
            if settings.ENVIRONMENT != "production"
            and (settings_mail_to := Settings.get_to_email())
            else settings.ACM_NOTICEBOARD_EMAIL
        )

        subject = f"{self.masthead.site_name} received a new contribution"
        dashboard_url = f"{settings.LONGBEACH_HOST}manage/ugc/"
        context = {
            "dashboard_item_url": f"{dashboard_url}{self.pk}/",
            "dashboard_url": dashboard_url,
            "noticeboard_email": settings.ACM_NOTICEBOARD_EMAIL,
            "object": self,
            "omit_signature": True,
            "site_name": self.masthead.site_name,
        }

        send_html_email(
            "ugc/emails/new_content",
            context,
            subject,
            (to_email,),
        )

    def send_upcoming_event_email(self) -> None:
        if (
            self.content_type != ContentType.EVENT
            or not self._has_valid_user_profile()
        ):
            return None

        assert self.user_profile is not None

        subject = UserGeneratedContent.UPCOMING_EVENT_EMAIL_SUBJECT

        context = {
            "share_url": self.status == UserGeneratedContent.APPROVED
            and self.detail_page_url,
            "edit_url": self.edit_url,
            "noticeboard_email": settings.ACM_NOTICEBOARD_EMAIL,
            "object": self,
            "site_name": self.masthead.site_name,
            "text": [
                cleandoc(text)
                for text in UserGeneratedContent.UPCOMING_EVENT_EMAIL_TEXT
            ],
        }

        send_html_email(
            "ugc/emails/upcoming_event",
            context,
            subject,
            (self.user_profile.user_email,),
        )

        self.notifications.create(
            notification_type=NotificationType.UPCOMING_EVENT
        )

    def send_follow_up_email(self) -> None:
        if not self._has_valid_user_profile():
            return None

        assert self.user_profile is not None

        subject = (
            UserGeneratedContent.FOLLOW_UP_EVENT_EMAIL_SUBJECT
            if self.content_type == ContentType.EVENT
            else UserGeneratedContent.FOLLOW_UP_EMAIL_SUBJECT
        )
        texts = (
            UserGeneratedContent.FOLLOW_UP_EVENT_EMAIL_TEXT
            if self.content_type == ContentType.EVENT
            else UserGeneratedContent.FOLLOW_UP_EMAIL_TEXT
        )
        context = {
            "share_url": self.status == UserGeneratedContent.APPROVED
            and self.detail_page_url,
            "edit_url": self.edit_url,
            "noticeboard_email": settings.ACM_NOTICEBOARD_EMAIL,
            "object": self,
            "site_name": self.masthead.site_name,
            "text": [cleandoc(text) for text in texts],
        }

        send_html_email(
            "ugc/emails/follow_up",
            context,
            subject,
            (self.user_profile.user_email,),
        )

        self.notifications.create(notification_type=NotificationType.FOLLOW_UP)


@reversion.register()
class UGCImage(models.Model):
    ugc = models.ForeignKey(
        UserGeneratedContent,
        related_name="images",
        on_delete=models.deletion.CASCADE,
    )
    image = ImageField(storage=valencia_fs, upload_to="images")

    def get_uri(self):
        try:
            return f"longbeach/{self.image.url.rstrip('/').split('/')[-1]}"
        except ValueError:
            return None


@reversion.register()
class UGCNotification(models.Model):
    ugc = models.ForeignKey(
        UserGeneratedContent,
        related_name="notifications",
        on_delete=models.deletion.CASCADE,
    )
    notification_type = models.CharField(
        max_length=MAX_LENGTH, choices=NotificationType.choices
    )
    created_on = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = _("Notification")
        verbose_name_plural = _("Notifications")
        ordering = ("-created_on", "id")
